import 'dart:math';

import 'package:fl_chart/fl_chart.dart' as fl_charts;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:police/common/model/analyze_details_model/alarms_statistic.dart';
import 'package:police/common/model/analyze_details_model/sort.dart';
import 'package:police/common/model/analyze_details_model/village_list.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/util/logger.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/pie_chart.dart';
import 'package:police/page/analyze/controller.dart';
import 'package:police/page/analyzeDetail/index.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;

class AnalyzeTable extends StatelessWidget {
  const AnalyzeTable(
      {super.key, required this.controller, required this.detail});

  final AnalyzeModel detail;
  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    List<AlarmsStatistic> chain = controller.chain.value.alarmsStatistics!;
    List<AlarmsStatistic> yoy = controller.yoy.value.alarmsStatistics!;

    // 计算最大长度，确保不会超出任何列表的范围
    int maxLength = [datas.length, chain.length, yoy.length]
        .reduce((a, b) => a > b ? a : b);

    return Column(
      children: List.generate(maxLength + 1, (index) {
        List<Sort> sort = [];
        List<Sort> chainSort = [];
        List<Sort> yoySort = [];
        List<VillageList> villages = [];

        int j = index;
        if (index > 0) {
          j = index - 1;
          // 添加边界检查，防止索引超出范围
          if (j < datas.length) {
            sort = datas[j].sort ?? [];
          }
          if (j < chain.length) {
            chainSort = chain[j].sort ?? [];
          }
          if (j < yoy.length) {
            yoySort = yoy[j].sort ?? [];
          }
        } else {
          villages = controller.details.villageList ?? [];
        }

        /// 警情总数
        int total = 0;
        // 计算警情总数
        for (var element in villages) {
          total += element.alarmCount ?? 0;
        }

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 50),
          child: CornerCard(
            child: index > 0
                ? _buildCaseTypeTable(
                    datas, chain, yoy, j, sort, chainSort, yoySort)
                : _buildAeraCaseTable(villages, total),
          ),
        );
      }),
    );
  }

  /// 预定义的颜色列表
  static const List<String> _predefinedColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青色
    '#45B7D1', // 蓝色
    '#96CEB4', // 绿色
    '#FFEAA7', // 黄色
    '#DDA0DD', // 紫色
    '#98D8C8', // 薄荷绿
    '#F7DC6F', // 金黄色
    '#BB8FCE', // 淡紫色
    '#85C1E9', // 天蓝色
    '#F8C471', // 橙色
    '#82E0AA', // 浅绿色
  ];

  /// 区域发案表格
  Column _buildAeraCaseTable(List<VillageList> villages, int total) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '${_getYearMonth(detail)}警情发案区域统计表',
          style: const TextStyle(fontSize: 25, color: Colors.white),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: List.generate(villages.length, (i) {
            return Column(
              children: [
                Text(
                  villages[i].name ?? '',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  villages[i].alarmCount.toString(),
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            );
          }),
        ),
        if (total > 0)
          Column(
            children: [
              const SizedBox(height: 10),
              PieChart(
                [
                  charts.Series(
                    id: 'area',
                    data: villages,
                    domainFn: (datum, _) => datum.name ?? '',
                    measureFn: (datum, _) => datum.alarmCount,
                    labelAccessorFn: (datum, index) =>
                        (datum.alarmCount / total * 100).toStringAsFixed(1) +
                        '%',
                    colorFn: (datum, index) => charts.Color.fromHex(
                        code: _predefinedColors[
                            index! % _predefinedColors.length]),
                  )
                ],
                showBehavior: true,
                position: charts.BehaviorPosition.bottom,
              ),
            ],
          )
      ],
    );
  }

  /// 警情类型表格
  Column _buildCaseTypeTable(
    List<AlarmsStatistic> datas,
    List<AlarmsStatistic> chain,
    List<AlarmsStatistic> yoy,
    int j,
    List<Sort> sort,
    List<Sort> chainSort,
    List<Sort> yoySort,
  ) {
    // 添加边界检查
    String tableName = '';
    if (j < datas.length) {
      tableName = datas[j].name ?? '';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '${_getYearMonth(detail)}${tableName}警情统计表',
          style: const TextStyle(fontSize: 25, color: Colors.white),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: List.generate(sort.length + 1, (i) {
            return Container(
              width: 100,
              alignment: Alignment.center,
              child: Column(
                children: [
                  Text(
                    i == 0 ? '合计（起）' : "${sort[i - 1].name}（起）",
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.white),
                  ),
                  Text(
                    i == 0
                        ? (j < datas.length
                            ? datas[j].alarmCount.toString()
                            : '0')
                        : sort[i - 1].count.toString(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            );
          }),
        ),
        // if ((j < datas.length && datas[j].alarmCount > 0) ||
        //     (j < chain.length && chain[j].alarmCount > 0))
        //   Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       const SizedBox(height: 50),
        //       Padding(
        //         padding: const EdgeInsets.all(10),
        //         child: Row(
        //           children: [
        //             Text(
        //               '${j < datas.length ? datas[j].name ?? '' : ''}警情环比',
        //               style: const TextStyle(fontSize: 20, color: Colors.white),
        //             ),
        //             const SizedBox(width: 30),
        //             Container(
        //               width: 10,
        //               height: 10,
        //               color: Colors.blue,
        //             ),
        //             const SizedBox(width: 10),
        //             Text(
        //               '本周期',
        //               style: const TextStyle(fontSize: 15, color: Colors.white),
        //             ),
        //             const SizedBox(width: 20),
        //             Container(
        //               width: 10,
        //               height: 10,
        //               color: Colors.cyan,
        //             ),
        //             const SizedBox(width: 10),
        //             Text(
        //               '上周期',
        //               style: const TextStyle(fontSize: 15, color: Colors.white),
        //             ),
        //           ],
        //         ),
        //       ),
        //       SizedBox(
        //         height: 200,
        //         child: fl_charts.BarChart(
        //           fl_charts.BarChartData(
        //             // maxY: datas[j].alarmCount.toDouble(),
        //             barGroups: _buildBarGroups(sort, chainSort),
        //             borderData: fl_charts.FlBorderData(show: false),
        //             gridData: const fl_charts.FlGridData(show: false),
        //             titlesData: _buildBarChartTitles(
        //                 _normalizeSortLists(sort, chainSort)['sort']!),
        //             barTouchData: _buildBarChartTouthData(),
        //           ),
        //         ),
        //       ),
        //     ],
        //   ),
        // if ((j < datas.length && datas[j].alarmCount > 0) ||
        //     (j < yoy.length && yoy[j].alarmCount > 0))
        //   Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       const SizedBox(height: 50),
        //       Padding(
        //         padding: const EdgeInsets.all(10),
        //         child: Row(
        //           children: [
        //             Text(
        //               '${j < datas.length ? datas[j].name ?? '' : ''}警情同比',
        //               style: const TextStyle(fontSize: 20, color: Colors.white),
        //             ),
        //             const SizedBox(width: 30),
        //             Container(
        //               width: 10,
        //               height: 10,
        //               color: Colors.blue,
        //             ),
        //             const SizedBox(width: 10),
        //             Text(
        //               '本周期',
        //               style: const TextStyle(fontSize: 15, color: Colors.white),
        //             ),
        //             const SizedBox(width: 20),
        //             Container(
        //               width: 10,
        //               height: 10,
        //               color: Colors.cyan,
        //             ),
        //             const SizedBox(width: 10),
        //             Text(
        //               '上周期',
        //               style: const TextStyle(fontSize: 15, color: Colors.white),
        //             ),
        //           ],
        //         ),
        //       ),
        //       SizedBox(
        //         height: 200,
        //         child: fl_charts.BarChart(
        //           fl_charts.BarChartData(
        //             // maxY: datas[j].alarmCount.toDouble(),
        //             barGroups: _buildBarGroups(sort, yoySort),
        //             borderData: fl_charts.FlBorderData(show: false),
        //             gridData: const fl_charts.FlGridData(show: false),
        //             titlesData: _buildBarChartTitles(
        //                 _normalizeSortLists(sort, yoySort)['sort']!),
        //             barTouchData: _buildBarChartTouthData(),
        //           ),
        //         ),
        //       ),
        //     ],
        //   ),
      ],
    );
  }

  /// Tooltip 样式
  fl_charts.BarTouchData _buildBarChartTouthData() {
    return fl_charts.BarTouchData(
      touchTooltipData: fl_charts.BarTouchTooltipData(
        getTooltipItem: (group, groupIndex, rod, rodIndex) =>
            fl_charts.BarTooltipItem(
          rod.toY.round().toString(),
          const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        getTooltipColor: (group) => Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 8,
      ),
    );
  }

  /// 标题样式
  fl_charts.FlTitlesData _buildBarChartTitles(List<Sort> sort) {
    return fl_charts.FlTitlesData(
        leftTitles: _hideTitles(),
        rightTitles: _hideTitles(),
        topTitles: _hideTitles(),
        bottomTitles: fl_charts.AxisTitles(
          sideTitles: fl_charts.SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              return fl_charts.SideTitleWidget(
                axisSide: meta.axisSide,
                child: Text(sort[value.toInt()].name ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    )),
              );
            },
          ),
        ));
  }

  /// 统一两个列表的长度，短的补充缺失的项目，并按name排序
  Map<String, List<Sort>> _normalizeSortLists(
      List<Sort> sort, List<Sort> chain) {
    List<Sort> normalizedSort = List.from(sort);
    List<Sort> normalizedChain = List.from(chain);

    if (sort.length > chain.length) {
      // sort 比 chain 长，给 chain 补充缺失的项目
      for (var sortItem in sort) {
        bool found = chain.any((chainItem) => chainItem.name == sortItem.name);
        if (!found) {
          normalizedChain.add(Sort(name: sortItem.name, count: 0));
        }
      }
    } else if (sort.length < chain.length) {
      // chain 比 sort 长，给 sort 补充缺失的项目
      for (var chainItem in chain) {
        bool found = sort.any((sortItem) => sortItem.name == chainItem.name);
        if (!found) {
          normalizedSort.add(Sort(name: chainItem.name, count: 0));
        }
      }
    }

    // 按 name 排序两个列表
    normalizedSort.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    normalizedChain.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    return {
      'sort': normalizedSort,
      'chain': normalizedChain,
    };
  }

  /// 柱状图样式
  List<fl_charts.BarChartGroupData> _buildBarGroups(
      List<Sort> sort, List<Sort> chain) {
    Map<String, List<Sort>> normalized = _normalizeSortLists(sort, chain);
    List<Sort> normalizedSort = normalized['sort']!;
    List<Sort> normalizedChain = normalized['chain']!;

    return List.generate(normalizedSort.length, (index) {
      // 找到对应的 chain 项目
      Sort? correspondingChain;
      try {
        correspondingChain = normalizedChain.firstWhere(
            (chainItem) => chainItem.name == normalizedSort[index].name);
      } catch (e) {
        correspondingChain = null;
      }

      return fl_charts.BarChartGroupData(
        x: index,
        barRods: [
          fl_charts.BarChartRodData(
            toY: normalizedSort[index].count!.toDouble(),
            color: Colors.blue,
            // width: 13,
            // backDrawRodData: fl_charts
            //     .BackgroundBarChartRodData(
            //   show: true,
            //   toY: datas[j]
            //       .alarmCount
            //       .toDouble(),
            //   color:
            //       Colors.white.withOpacity(.3),
            // ),
          ),
          fl_charts.BarChartRodData(
            toY: (correspondingChain?.count ?? 0).toDouble(),
            color: Colors.cyan,
          ),
        ],
      );
    });
  }

  /// 隐藏标题
  fl_charts.AxisTitles _hideTitles() {
    return const fl_charts.AxisTitles(
      sideTitles: fl_charts.SideTitles(
        showTitles: false,
      ),
    );
  }

  /// 获取年月
  String _getYearMonth(AnalyzeModel detail) {
    String str = '';

    switch (detail.types) {
      case AnalyzeSortTypes.month:
        str = '${detail.yearMonthDay!.split('-')[1]}月份';

        break;
      case AnalyzeSortTypes.quarter:
        str =
            '(${detail.yearMonthDay!.split('-')[0]})第${controller.getQuarter(detail)}季度';

        break;
      case AnalyzeSortTypes.year:
        str = '${detail.yearMonthDay!.split('-')[0]}年';

        break;
      default:
    }

    return str;
  }
}
